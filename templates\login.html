{% extends "base.html" %}

{% block title %}Login - Kawaii Chat 🌸{% endblock %}

{% block content %}
<div class="auth-container">
    <div class="auth-card">
        <!-- Kawaii Mascot -->
        <div class="kawaii-mascot">
            <div class="mascot-face">
                <div class="eyes">
                    <div class="eye left"></div>
                    <div class="eye right"></div>
                </div>
                <div class="mouth"></div>
                <div class="blush left-blush"></div>
                <div class="blush right-blush"></div>
            </div>
            <div class="mascot-text">Welcome Back! 💕</div>
        </div>
        
        <!-- Login Form -->
        <form method="POST" class="auth-form">
            <h1 class="auth-title">
                <i class="fas fa-heart"></i>
                Login to Kawaii Chat
                <i class="fas fa-heart"></i>
            </h1>
            
            <div class="form-group">
                <label for="username">
                    <i class="fas fa-user"></i>
                    Username
                </label>
                <input type="text" id="username" name="username" required 
                       placeholder="Enter your kawaii username... ✨">
            </div>
            
            <div class="form-group">
                <label for="password">
                    <i class="fas fa-lock"></i>
                    Password
                </label>
                <input type="password" id="password" name="password" required 
                       placeholder="Enter your secret password... 🔒">
            </div>
            
            <button type="submit" class="btn-primary">
                <i class="fas fa-sign-in-alt"></i>
                Login
                <span class="btn-sparkle">✨</span>
            </button>
        </form>
        
        <!-- Signup Link -->
        <div class="auth-footer">
            <p>Don't have an account? 🦄</p>
            <a href="{{ url_for('signup') }}" class="auth-link">
                <i class="fas fa-user-plus"></i>
                Create Account
            </a>
        </div>
    </div>
    
    <!-- Decorative Elements -->
    <div class="auth-decorations">
        <div class="decoration star-1">⭐</div>
        <div class="decoration star-2">🌟</div>
        <div class="decoration heart-1">💖</div>
        <div class="decoration heart-2">💕</div>
        <div class="decoration unicorn">🦄</div>
        <div class="decoration rainbow">🌈</div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add kawaii animations to form
    const form = document.querySelector('.auth-form');
    const inputs = form.querySelectorAll('input');
    
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
            createHeartBurst(this);
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
    
    // Animate mascot
    const mascot = document.querySelector('.mascot-face');
    setInterval(() => {
        mascot.classList.add('blink');
        setTimeout(() => mascot.classList.remove('blink'), 200);
    }, 3000);
    
    // Form submission animation
    form.addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('.btn-primary');
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in... 🌸';
        submitBtn.disabled = true;
    });
});

function createHeartBurst(element) {
    const hearts = ['💕', '💖', '💗', '💘', '💝', '💞'];
    const rect = element.getBoundingClientRect();
    
    for (let i = 0; i < 5; i++) {
        const heart = document.createElement('div');
        heart.className = 'heart-burst';
        heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.left = (rect.left + Math.random() * rect.width) + 'px';
        heart.style.top = rect.top + 'px';
        
        document.body.appendChild(heart);
        
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 1000);
    }
}
</script>
{% endblock %}
