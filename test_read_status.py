#!/usr/bin/env python3
"""
Test script to verify read status functionality
"""
import sqlite3
from datetime import datetime

def test_read_status():
    print("🧪 Testing read status functionality...")
    
    # Connect to database
    conn = sqlite3.connect('kawaii_chat.db')
    cursor = conn.cursor()
    
    # Check current read status of recent messages
    print("\n📊 Current read status of recent messages:")
    cursor.execute('''
        SELECT id, sender_id, receiver_id, message, read_at
        FROM messages 
        WHERE id >= 110
        ORDER BY id DESC
        LIMIT 10
    ''')
    
    messages = cursor.fetchall()
    for msg in messages:
        read_status = "✓✓ READ" if msg[4] else "✓ SENT"
        print(f"   ID {msg[0]}: {msg[3][:30]}... - {read_status}")
    
    # Simulate marking messages as read (like when user opens chat)
    print(f"\n📖 Simulating user 1 opening chat with user 2...")
    print("   This should mark all messages from user 2 to user 1 as read")
    
    # Mark messages from user 2 to user 1 as read
    cursor.execute('''
        UPDATE messages
        SET read_at = datetime('now')
        WHERE receiver_id = 1 AND sender_id = 2 AND read_at IS NULL
    ''')
    
    affected_rows = cursor.rowcount
    print(f"   ✅ Marked {affected_rows} messages as read")
    
    conn.commit()
    
    # Check updated read status
    print(f"\n📊 Updated read status:")
    cursor.execute('''
        SELECT id, sender_id, receiver_id, message, read_at
        FROM messages 
        WHERE id >= 110
        ORDER BY id DESC
        LIMIT 10
    ''')
    
    messages = cursor.fetchall()
    for msg in messages:
        read_status = "✓✓ READ" if msg[4] else "✓ SENT"
        direction = f"User {msg[1]} → User {msg[2]}"
        print(f"   ID {msg[0]}: {direction} - {msg[3][:30]}... - {read_status}")
    
    # Test the frontend condition
    print(f"\n🔍 Testing frontend read status logic:")
    for msg in messages:
        message_data = {
            'id': msg[0],
            'sender_id': msg[1],
            'receiver_id': msg[2],
            'message': msg[3],
            'read_at': msg[4],
            'is_own': msg[1] == 1  # Assuming we're testing as user 1
        }
        
        # Frontend logic for read status
        is_read = (message_data.get('read_at') and
                  message_data['read_at'] != '' and
                  message_data['read_at'] != 'null')
        
        if message_data['is_own']:
            status_display = "✓✓ (read)" if is_read else "✓ (sent)"
            print(f"   Message {msg[0]} (own): {status_display}")
    
    conn.close()
    
    print(f"\n✅ Read status test completed!")
    print(f"   - Messages are now properly marked as read when users open chats")
    print(f"   - Frontend will show ✓✓ for read messages and ✓ for sent messages")
    print(f"   - Only sender's own messages show read receipts")

if __name__ == "__main__":
    test_read_status()
