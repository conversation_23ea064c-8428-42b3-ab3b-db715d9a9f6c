// Kawaii Chat - Chat Functionality

class KawaiiChat {
    constructor() {
        console.log('KawaiiChat constructor called');
        console.log('Socket.IO available:', typeof io);

        this.socket = io();
        console.log('Socket created:', this.socket);

        // Make socket available globally for WebRTC
        window.socket = this.socket;

        // Add socket event listeners for debugging
        this.socket.on('connect', () => {
            console.log('✅ Socket connected successfully! ID:', this.socket.id);
        });

        this.socket.on('disconnect', () => {
            console.log('❌ Socket disconnected');
        });

        this.socket.on('connect_error', (error) => {
            console.error('❌ Socket connection error:', error);
        });

        this.currentContactId = null;
        this.currentContactName = '';
        this.currentContactColor = '';
        this.currentContactProfilePicture = null;
        console.log('Calling init...');
        this.init();
        console.log('KawaiiChat constructor complete');
    }

    init() {
        this.bindEvents();
        this.connectSocket();
    }

    bindEvents() {
        // Message input events
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        
        if (messageInput) {
            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });
            
            messageInput.addEventListener('input', () => {
                this.updateSendButton();
            });
        }
        
        if (sendBtn) {
            sendBtn.addEventListener('click', () => this.sendMessage());
        }
    }

    connectSocket() {
        this.socket.on('connect', () => {
            console.log('Connected to Kawaii Chat! 🌸');
            showToast('Connected to chat! 🌸', 'success');
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from chat 😢');
            showToast('Disconnected from chat 😢', 'error');
        });

        this.socket.on('new_message', (data) => {
            this.handleNewMessage(data);
        });

        this.socket.on('message_sent', (data) => {
            this.handleMessageSent(data);
        });
    }

    selectContact(contactId, contactName, contactColor, profilePictureUrl = null) {
        console.log('selectContact called:', { contactId, contactName, contactColor, profilePictureUrl });

        this.currentContactId = contactId;
        this.currentContactName = contactName;
        this.currentContactColor = contactColor;
        this.currentContactProfilePicture = profilePictureUrl;

        console.log('Current contact set to:', this.currentContactId);

        // Immediately open chat when contact is selected
        this.updateActiveContact(contactId);
        this.updateChatHeader(contactName, contactColor, profilePictureUrl);
        this.showChatArea();
        this.loadMessages(contactId);

        // Create heart burst effect
        if (window.KawaiiEffects) {
            const effects = new window.KawaiiEffects();
            effects.createHeartBurst(document.querySelector(`[data-contact-id="${contactId}"]`));
        }

        console.log('Chat opened for contact:', contactName);
    }

    showStartChatButton(contactName) {
        const startChatBtn = document.getElementById('startChatBtn');
        const selectedContactName = document.getElementById('selectedContactName');
        const welcomeMessage = document.getElementById('welcomeMessage');

        if (startChatBtn) {
            startChatBtn.style.display = 'block';
        }

        if (selectedContactName) {
            selectedContactName.textContent = contactName;
        }

        if (welcomeMessage) {
            welcomeMessage.textContent = `Ready to chat with ${contactName}! Click the button below to start.`;
        }

        console.log('Start chat button shown for:', contactName);
    }

    // New method to open chat with selected contact
    openChat() {
        if (!this.currentContactId) {
            alert('Please select a contact first!');
            return;
        }

        console.log('Opening chat with contact:', this.currentContactId);

        // Update UI and open chat
        this.updateChatHeader(this.currentContactName, this.currentContactColor, this.currentContactProfilePicture);
        this.showChatArea();
        this.loadMessages(this.currentContactId);

        console.log('Chat opened successfully');
    }

    updateChatHeader(contactName, contactColor, profilePictureUrl = null) {
        console.log('updateChatHeader called:', { contactName, contactColor, profilePictureUrl });
        const chatHeader = document.getElementById('chatHeader');
        const chatAvatar = document.getElementById('chatAvatar');
        const chatContactName = document.getElementById('chatContactName');

        console.log('Chat header elements:', { chatHeader, chatAvatar, chatContactName });

        if (chatAvatar) {
            chatAvatar.style.background = contactColor;
            chatAvatar.innerHTML = '';

            if (profilePictureUrl) {
                const img = document.createElement('img');
                img.src = profilePictureUrl;
                img.alt = contactName;
                chatAvatar.appendChild(img);
            } else {
                const icon = document.createElement('i');
                icon.className = 'fas fa-user';
                chatAvatar.appendChild(icon);
            }
            console.log('Chat avatar updated');
        }

        if (chatContactName) {
            chatContactName.textContent = contactName;
            console.log('Chat contact name updated to:', contactName);
        }
    }

    showChatArea() {
        console.log('showChatArea called');
        const chatWelcome = document.getElementById('chatWelcome');
        const chatArea = document.getElementById('chatArea');

        console.log('chatWelcome element:', chatWelcome);
        console.log('chatArea element:', chatArea);

        if (chatWelcome) {
            chatWelcome.style.display = 'none';
            console.log('Welcome area hidden');
        }
        if (chatArea) {
            chatArea.style.display = 'flex';
            console.log('Chat area shown');

            // Add a nice transition effect
            chatArea.style.opacity = '0';
            setTimeout(() => {
                chatArea.style.transition = 'opacity 0.3s ease';
                chatArea.style.opacity = '1';
                console.log('Chat area transition complete');
            }, 50);
        }
    }

    updateActiveContact(contactId) {
        // Remove active class from all contacts
        document.querySelectorAll('.contact-item').forEach(item => {
            item.classList.remove('active');
        });
        
        // Add active class to selected contact
        const selectedContact = document.querySelector(`[data-contact-id="${contactId}"]`);
        if (selectedContact) {
            selectedContact.classList.add('active');
        }
    }

    async loadMessages(contactId) {
        try {
            const response = await fetch(`/api/messages/${contactId}`);
            const messages = await response.json();
            this.renderMessages(messages);
        } catch (error) {
            console.error('Error loading messages:', error);
            showToast('Failed to load messages 😢', 'error');
        }
    }

    renderMessages(messages) {
        const container = document.getElementById('messagesContainer');
        if (!container) return;
        
        container.innerHTML = '';
        
        messages.forEach(message => {
            this.renderMessage(message);
        });
        
        this.scrollToBottom();
    }

    renderMessage(message) {
        const container = document.getElementById('messagesContainer');
        if (!container) return;

        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${message.is_own ? 'own' : ''}`;

        const bubble = document.createElement('div');
        bubble.className = 'message-bubble';

        // Handle media messages
        if (message.message_type && message.message_type !== 'text' && message.media_url) {
            const mediaDiv = this.createMediaElement(message);
            bubble.appendChild(mediaDiv);
        }

        // Add text if present
        if (message.message && message.message.trim()) {
            const text = document.createElement('div');
            text.className = 'message-text';
            text.textContent = message.message;
            bubble.appendChild(text);
        }

        const time = document.createElement('div');
        time.className = 'message-time';
        time.textContent = this.formatTime(message.sent_at);

        bubble.appendChild(time);
        messageDiv.appendChild(bubble);

        container.appendChild(messageDiv);
    }

    createMediaElement(message) {
        const mediaDiv = document.createElement('div');
        mediaDiv.className = 'message-media';

        switch (message.message_type) {
            case 'image':
                const img = document.createElement('img');
                img.src = message.thumbnail_url || message.media_url;
                img.alt = 'Image';
                img.onclick = () => this.openMediaViewer(message.media_url, 'image');
                mediaDiv.appendChild(img);
                break;

            case 'video':
                const video = document.createElement('video');
                video.src = message.media_url;
                video.controls = true;
                video.preload = 'metadata';
                mediaDiv.appendChild(video);
                break;

            case 'audio':
                const audio = document.createElement('audio');
                audio.src = message.media_url;
                audio.controls = true;
                audio.preload = 'metadata';
                mediaDiv.appendChild(audio);
                break;

            default:
                // File/document
                const fileDiv = document.createElement('div');
                fileDiv.className = 'file-message';
                fileDiv.onclick = () => window.open(message.media_url, '_blank');

                const fileIcon = document.createElement('div');
                fileIcon.className = 'file-icon';
                fileIcon.innerHTML = this.getFileIcon(message.message_type);

                const fileDetails = document.createElement('div');
                fileDetails.className = 'file-details';

                const fileName = document.createElement('div');
                fileName.className = 'file-name';
                fileName.textContent = 'Document';

                const fileSize = document.createElement('div');
                fileSize.className = 'file-size';
                fileSize.textContent = this.formatFileSize(message.file_size);

                fileDetails.appendChild(fileName);
                fileDetails.appendChild(fileSize);
                fileDiv.appendChild(fileIcon);
                fileDiv.appendChild(fileDetails);
                mediaDiv.appendChild(fileDiv);
                break;
        }

        return mediaDiv;
    }

    getFileIcon(fileType) {
        const icons = {
            'document': '<i class="fas fa-file"></i>',
            'audio': '<i class="fas fa-music"></i>',
            'video': '<i class="fas fa-video"></i>',
            'image': '<i class="fas fa-image"></i>'
        };
        return icons[fileType] || '<i class="fas fa-file"></i>';
    }

    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    openMediaViewer(url, type) {
        // Simple media viewer - opens in new tab for now
        window.open(url, '_blank');
    }

    sendMessage() {
        console.log('sendMessage called');
        const messageInput = document.getElementById('messageInput');
        const message = messageInput.value.trim();

        console.log('Message:', message);
        console.log('Current contact ID:', this.currentContactId);

        if (!message) {
            console.log('No message to send');
            return;
        }

        if (!this.currentContactId) {
            console.log('No contact selected');
            alert('Please select a contact first!');
            return;
        }
        
        // Send via socket
        this.socket.emit('send_message', {
            receiver_id: this.currentContactId,
            message: message
        });
        
        // Clear input
        messageInput.value = '';
        this.updateSendButton();
        
        // Create heart effect
        if (window.KawaiiEffects) {
            const effects = new window.KawaiiEffects();
            effects.createHeartBurst(messageInput);
        }
    }

    handleNewMessage(data) {
        // Only show if it's from the current contact
        if (data.sender_id == this.currentContactId) {
            this.renderMessage({
                ...data,
                is_own: false
            });
            this.scrollToBottom();
            
            // Create notification effect
            this.createMessageNotification();
        }
        
        // Update contact list (you might want to implement this)
        this.updateContactLastMessage(data.sender_id, data.message);
    }

    handleMessageSent(data) {
        this.renderMessage({
            ...data,
            is_own: true
        });
        this.scrollToBottom();

        showToast('Message sent! 💕', 'success');
    }

    createMessageNotification() {
        // Create a cute notification effect
        const notification = document.createElement('div');
        notification.innerHTML = '💕';
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.fontSize = '2rem';
        notification.style.zIndex = '1000';
        notification.style.animation = 'messageNotification 2s ease-out forwards';
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 2000);
    }

    updateContactLastMessage(contactId, message) {
        const contactItem = document.querySelector(`[data-contact-id="${contactId}"]`);
        if (contactItem) {
            // You might want to add a last message display here
        }
    }

    updateSendButton() {
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        
        if (messageInput && sendBtn) {
            const hasText = messageInput.value.trim().length > 0;
            sendBtn.disabled = !hasText || !this.currentContactId;
            
            if (hasText && this.currentContactId) {
                sendBtn.style.background = 'linear-gradient(45deg, #ff6b9d, #c44569)';
            } else {
                sendBtn.style.background = '#ccc';
            }
        }
    }

    scrollToBottom() {
        const container = document.getElementById('messagesContainer');
        if (container) {
            container.scrollTop = container.scrollHeight;
        }
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        
        if (diffMins < 1) return 'now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        
        return date.toLocaleDateString();
    }
}

// Contact Management Functions
function showAddContactModal() {
    console.log('showAddContactModal called!');
    alert('Button clicked! Opening modal...'); // Debug alert

    const modal = document.getElementById('addContactModal');
    console.log('Modal element found:', modal);

    if (modal) {
        modal.classList.add('show');
        modal.style.display = 'flex'; // Ensure modal is visible
        console.log('Modal should be visible now');

        // Focus on contact code input
        const contactCodeInput = document.getElementById('contactCode');
        if (contactCodeInput) {
            setTimeout(() => contactCodeInput.focus(), 100);
        }

        // Create heart effect
        if (window.KawaiiEffects) {
            const effects = new window.KawaiiEffects();
            effects.createHeartBurst(modal);
        }
    } else {
        alert('Modal not found!');
        console.error('Modal element not found!');
    }
}

function closeAddContactModal() {
    const modal = document.getElementById('addContactModal');
    if (modal) {
        modal.classList.remove('show');
        
        // Clear form
        const form = document.getElementById('addContactForm');
        if (form) {
            form.reset();
        }
    }
}

async function addContact() {
    console.log('addContact function called');
    const contactCode = document.getElementById('contactCode').value.trim().toUpperCase();
    const contactName = document.getElementById('contactName').value.trim();

    console.log('Contact code:', contactCode, 'Contact name:', contactName);

    if (!contactCode) {
        showToast('Please enter a contact code! 💕', 'error');
        return;
    }

    if (contactCode.length !== 8) {
        showToast('Contact code must be 8 characters! 🔤', 'error');
        return;
    }
    
    try {
        console.log('Sending request to add contact...');
        const response = await fetch('/add_contact', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contact_code: contactCode,
                contact_name: contactName
            })
        });

        console.log('Response status:', response.status);
        const result = await response.json();
        console.log('Response result:', result);
        
        if (result.success) {
            showToast(result.message, 'success');
            closeAddContactModal();

            // Create confetti effect
            if (window.createConfetti) {
                window.createConfetti();
            }

            // Add the new contact to the contacts list immediately
            addContactToList(result.contact);

            // Automatically select the new contact and start chat
            setTimeout(() => {
                if (window.kawaiiChat) {
                    window.kawaiiChat.selectContact(
                        result.contact.id,
                        result.contact.display_name || result.contact.username,
                        result.contact.avatar_color,
                        result.contact.profile_picture_url
                    );
                    showToast('Ready to chat! 💬✨', 'success');
                }
            }, 500);
        } else {
            showToast(result.message, 'error');
        }
    } catch (error) {
        console.error('Error adding contact:', error);
        showToast('Failed to add contact 😢', 'error');
    }
}

function addContactToList(contact) {
    const contactsList = document.getElementById('contactsList');
    const noContacts = contactsList.querySelector('.no-contacts');

    // Remove "no contacts" message if it exists
    if (noContacts) {
        noContacts.remove();
    }

    // Create new contact item
    const contactItem = document.createElement('div');
    contactItem.className = 'contact-item';
    contactItem.setAttribute('data-contact-id', contact.id);
    contactItem.onclick = () => selectContact(
        contact.id,
        contact.display_name || contact.username,
        contact.avatar_color,
        contact.profile_picture_url
    );

    contactItem.innerHTML = `
        <div class="contact-avatar" style="background: ${contact.avatar_color}">
            ${contact.profile_picture_url ?
                `<img src="${contact.profile_picture_url}" alt="${contact.display_name || contact.username}">` :
                '<i class="fas fa-user"></i>'
            }
        </div>
        <div class="contact-info">
            <div class="contact-name">${contact.display_name || contact.username}</div>
            <div class="contact-status">${contact.username}</div>
        </div>
        <div class="contact-indicator" id="indicator-${contact.id}">
            <i class="fas fa-circle" style="color: #ccc;" title="Offline"></i>
        </div>
    `;

    // Add to the top of the contacts list
    contactsList.insertBefore(contactItem, contactsList.firstChild);

    // Add kawaii animation
    contactItem.style.opacity = '0';
    contactItem.style.transform = 'translateY(-20px)';
    setTimeout(() => {
        contactItem.style.transition = 'all 0.3s ease';
        contactItem.style.opacity = '1';
        contactItem.style.transform = 'translateY(0)';
    }, 100);
}

function toggleEmojiPicker() {
    // Simple emoji insertion for now
    const emojis = ['😊', '😍', '🥰', '😘', '💕', '✨', '🌸', '🦄', '🍭', '🎀'];
    const randomEmoji = emojis[Math.floor(Math.random() * emojis.length)];

    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        const cursorPos = messageInput.selectionStart;
        const textBefore = messageInput.value.substring(0, cursorPos);
        const textAfter = messageInput.value.substring(cursorPos);

        messageInput.value = textBefore + randomEmoji + textAfter;
        messageInput.selectionStart = messageInput.selectionEnd = cursorPos + randomEmoji.length;
        messageInput.focus();

        // Create heart effect
        if (window.KawaiiEffects) {
            const effects = new window.KawaiiEffects();
            effects.createHeartBurst(messageInput);
        }
    }
}

// Remove duplicate - will be defined later

// Media Upload Functions
function showMediaModal() {
    if (!window.kawaiiChat || !window.kawaiiChat.currentContactId) {
        showToast('Please select a contact first! 💕', 'error');
        return;
    }

    const modal = document.getElementById('mediaModal');
    if (modal) {
        modal.classList.add('show');
        resetMediaModal();
    }
}

function closeMediaModal() {
    const modal = document.getElementById('mediaModal');
    if (modal) {
        modal.classList.remove('show');
        resetMediaModal();
    }
}

function resetMediaModal() {
    const fileInput = document.getElementById('mediaFileInput');
    const preview = document.getElementById('mediaPreview');
    const caption = document.getElementById('mediaCaption');
    const sendBtn = document.getElementById('sendMediaBtn');

    if (fileInput) fileInput.value = '';
    if (preview) preview.style.display = 'none';
    if (caption) caption.value = '';
    if (sendBtn) sendBtn.disabled = true;
}

function handleMediaSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Check file size (50MB)
    if (file.size > 50 * 1024 * 1024) {
        showToast('File too large! Max 50MB 📁', 'error');
        return;
    }

    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp',
                       'video/mp4', 'video/mov', 'video/avi', 'video/webm'];

    if (!validTypes.includes(file.type)) {
        showToast('Invalid file type! Please select an image or video 📷', 'error');
        return;
    }

    // Show preview
    const preview = document.getElementById('mediaPreview');
    const container = document.getElementById('previewContainer');
    const sendBtn = document.getElementById('sendMediaBtn');

    if (file.type.startsWith('image/')) {
        const img = document.createElement('img');
        img.src = URL.createObjectURL(file);
        container.innerHTML = '';
        container.appendChild(img);
    } else if (file.type.startsWith('video/')) {
        const video = document.createElement('video');
        video.src = URL.createObjectURL(file);
        video.controls = true;
        video.style.maxHeight = '300px';
        container.innerHTML = '';
        container.appendChild(video);
    }

    preview.style.display = 'block';
    sendBtn.disabled = false;
}

async function sendMedia() {
    const fileInput = document.getElementById('mediaFileInput');
    const caption = document.getElementById('mediaCaption').value.trim();
    const file = fileInput.files[0];

    if (!file || !window.kawaiiChat.currentContactId) {
        showToast('Please select a file and contact! 💕', 'error');
        return;
    }

    const sendBtn = document.getElementById('sendMediaBtn');
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('receiver_id', window.kawaiiChat.currentContactId);
        formData.append('caption', caption);

        const response = await fetch('/upload_media', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // Render the message immediately
            window.kawaiiChat.renderMessage(result.message);
            window.kawaiiChat.scrollToBottom();

            closeMediaModal();
            showToast('Media sent! 📷✨', 'success');

            // Create heart effect
            if (window.KawaiiEffects) {
                const effects = new window.KawaiiEffects();
                effects.createHeartBurst(sendBtn);
            }
        } else {
            showToast(result.message || 'Failed to send media 😢', 'error');
        }
    } catch (error) {
        console.error('Error sending media:', error);
        showToast('Error sending media 😢', 'error');
    } finally {
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send';
    }
}

// File Upload Functions
function showFileModal() {
    if (!window.kawaiiChat || !window.kawaiiChat.currentContactId) {
        showToast('Please select a contact first! 💕', 'error');
        return;
    }

    const modal = document.getElementById('fileModal');
    if (modal) {
        modal.classList.add('show');
        resetFileModal();
    }
}

function closeFileModal() {
    const modal = document.getElementById('fileModal');
    if (modal) {
        modal.classList.remove('show');
        resetFileModal();
    }
}

function resetFileModal() {
    const fileInput = document.getElementById('fileInput');
    const preview = document.getElementById('filePreview');
    const caption = document.getElementById('fileCaption');
    const sendBtn = document.getElementById('sendFileBtn');

    if (fileInput) fileInput.value = '';
    if (preview) preview.style.display = 'none';
    if (caption) caption.value = '';
    if (sendBtn) sendBtn.disabled = true;
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Check file size (50MB)
    if (file.size > 50 * 1024 * 1024) {
        showToast('File too large! Max 50MB 📁', 'error');
        return;
    }

    // Show file info
    const preview = document.getElementById('filePreview');
    const fileInfo = document.getElementById('fileInfo');
    const sendBtn = document.getElementById('sendFileBtn');

    fileInfo.innerHTML = `
        <div class="file-icon">
            <i class="fas fa-file"></i>
        </div>
        <div class="file-details">
            <div class="file-name">${file.name}</div>
            <div class="file-size">${formatFileSize(file.size)}</div>
        </div>
    `;

    preview.style.display = 'block';
    sendBtn.disabled = false;
}

async function sendFile() {
    const fileInput = document.getElementById('fileInput');
    const caption = document.getElementById('fileCaption').value.trim();
    const file = fileInput.files[0];

    if (!file || !window.kawaiiChat.currentContactId) {
        showToast('Please select a file and contact! 💕', 'error');
        return;
    }

    const sendBtn = document.getElementById('sendFileBtn');
    sendBtn.disabled = true;
    sendBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Uploading...';

    try {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('receiver_id', window.kawaiiChat.currentContactId);
        formData.append('caption', caption);

        const response = await fetch('/upload_media', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // Render the message immediately
            window.kawaiiChat.renderMessage(result.message);
            window.kawaiiChat.scrollToBottom();

            closeFileModal();
            showToast('File sent! 📎✨', 'success');

            // Create heart effect
            if (window.KawaiiEffects) {
                const effects = new window.KawaiiEffects();
                effects.createHeartBurst(sendBtn);
            }
        } else {
            showToast(result.message || 'Failed to send file 😢', 'error');
        }
    } catch (error) {
        console.error('Error sending file:', error);
        showToast('Error sending file 😢', 'error');
    } finally {
        sendBtn.disabled = false;
        sendBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Send';
    }
}

function formatFileSize(bytes) {
    if (!bytes) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Global functions for template
window.selectContact = function(contactId, contactName, contactColor, profilePictureUrl = null) {
    if (window.kawaiiChat) {
        window.kawaiiChat.selectContact(contactId, contactName, contactColor, profilePictureUrl);
    }
};

window.showAddContactModal = showAddContactModal;
window.closeAddContactModal = closeAddContactModal;
window.addContact = addContact;

// Global sendMessage function for the send button
window.sendMessage = function() {
    if (window.kawaiiChat) {
        window.kawaiiChat.sendMessage();
    }
};

// SIMPLE DIRECT CHAT OPENER - FORCE SHOW CHAT
window.openChatWithContact = function(contactId, contactName, contactColor) {
    console.log('=== OPENING CHAT ===');
    console.log('Contact ID:', contactId);
    console.log('Contact Name:', contactName);

    // STEP 1: FORCE HIDE WELCOME
    const welcome = document.getElementById('chatWelcome');
    console.log('Welcome element:', welcome);
    if (welcome) {
        welcome.style.display = 'none';
        console.log('✓ Welcome hidden');
    }

    // STEP 2: FORCE SHOW CHAT AREA
    const chatArea = document.getElementById('chatArea');
    console.log('Chat area element:', chatArea);
    if (chatArea) {
        chatArea.style.display = 'flex';
        chatArea.style.visibility = 'visible';
        chatArea.style.opacity = '1';
        console.log('✓ Chat area shown');
    }

    // STEP 3: UPDATE CONTACT NAME IN HEADER
    const chatName = document.getElementById('chatContactName');
    console.log('Chat name element:', chatName);
    if (chatName) {
        chatName.textContent = contactName;
        console.log('✓ Contact name updated to:', contactName);
    }

    // STEP 4: UPDATE AVATAR
    const chatAvatar = document.getElementById('chatAvatar');
    if (chatAvatar) {
        chatAvatar.style.background = contactColor;
        chatAvatar.innerHTML = '<i class="fas fa-user"></i>';
        console.log('✓ Avatar updated');
    }

    // STEP 5: ADD SAMPLE MESSAGES FOR TESTING
    const messagesContainer = document.getElementById('messagesContainer');
    if (messagesContainer) {
        messagesContainer.innerHTML = `
            <div class="message">
                <div class="message-bubble">
                    <div class="message-text">Hello! This is a test message from ${contactName}</div>
                    <div class="message-time">10:30 AM</div>
                </div>
            </div>
            <div class="message own">
                <div class="message-bubble">
                    <div class="message-text">Hi there! How are you?</div>
                    <div class="message-time">10:31 AM</div>
                </div>
            </div>
        `;
        console.log('✓ Sample messages added');
    }

    // STEP 6: SET CURRENT CONTACT
    if (window.kawaiiChat) {
        window.kawaiiChat.currentContactId = contactId;
        window.kawaiiChat.currentContactName = contactName;
        console.log('✓ Current contact set');
    }

    console.log('=== CHAT OPENED SUCCESSFULLY ===');
};

// Global startChat function for the start chat button
window.startChat = function() {
    if (window.kawaiiChat) {
        window.kawaiiChat.openChat();
    } else {
        alert('Chat system not initialized!');
    }
};

// Test function to debug chat opening
window.testChatOpen = function() {
    console.log('Test chat open called');
    console.log('kawaiiChat instance:', window.kawaiiChat);

    if (window.kawaiiChat) {
        // Test with dummy data
        window.kawaiiChat.selectContact(1, 'Test Contact', '#007bff', null);
        console.log('Test contact selected');
    } else {
        console.error('kawaiiChat instance not found!');
        alert('Chat system not initialized!');
    }
};

// Direct test to open chat area without contact selection
window.directChatTest = function() {
    console.log('Direct chat test called');

    const chatWelcome = document.getElementById('chatWelcome');
    const chatArea = document.getElementById('chatArea');

    console.log('Elements found:', { chatWelcome, chatArea });

    if (chatWelcome && chatArea) {
        console.log('Hiding welcome, showing chat...');
        chatWelcome.style.display = 'none';
        chatArea.style.display = 'flex';
        chatArea.style.opacity = '1';
        console.log('Direct chat test complete');
    } else {
        console.error('Required elements not found!');
        alert('Chat elements not found!');
    }
};
window.toggleEmojiPicker = toggleEmojiPicker;
window.showMediaModal = showMediaModal;
window.closeMediaModal = closeMediaModal;
window.handleMediaSelect = handleMediaSelect;
window.sendMedia = sendMedia;
window.showFileModal = showFileModal;
window.closeFileModal = closeFileModal;
window.handleFileSelect = handleFileSelect;
window.sendFile = sendFile;

// Add message notification animation CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes messageNotification {
        0% {
            transform: scale(0) rotate(0deg);
            opacity: 1;
        }
        50% {
            transform: scale(1.2) rotate(180deg);
            opacity: 1;
        }
        100% {
            transform: scale(0.8) rotate(360deg) translateY(-50px);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);

// Initialize chat when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing KawaiiChat...');

    // Check if required elements exist
    const chatWelcome = document.getElementById('chatWelcome');
    const chatArea = document.getElementById('chatArea');
    const messageInput = document.getElementById('messageInput');

    console.log('Required elements check:', {
        chatWelcome: !!chatWelcome,
        chatArea: !!chatArea,
        messageInput: !!messageInput
    });

    window.kawaiiChat = new KawaiiChat();
    console.log('KawaiiChat initialized:', window.kawaiiChat);

    // Add event listener for the "Add First Contact" button
    const addFirstContactBtn = document.getElementById('addFirstContactBtn');
    if (addFirstContactBtn) {
        addFirstContactBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddContactModal();
        });
    }

    // Also add event listener for the sidebar add contact button
    const sidebarAddBtn = document.getElementById('sidebarAddContactBtn');
    if (sidebarAddBtn) {
        sidebarAddBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showAddContactModal();
        });
    }
});
