#!/usr/bin/env python3
"""
Test script to verify video upload functionality
"""
import requests
import json

# Test the video upload fix
def test_video_upload():
    print("🧪 Testing video upload functionality...")
    
    # First, let's check what message 114 looks like in the database
    import sqlite3
    conn = sqlite3.connect('kawaii_chat.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT id, sender_id, receiver_id, message, message_type, media_url, media_type, file_size
        FROM messages 
        WHERE id = 114
    ''')
    
    row = cursor.fetchone()
    if row:
        print(f"📁 Found video message in database:")
        print(f"   ID: {row[0]}")
        print(f"   Sender: {row[1]} -> Receiver: {row[2]}")
        print(f"   Message: {row[3]}")
        print(f"   Type: {row[4]}")
        print(f"   URL: {row[5]}")
        print(f"   Media Type: {row[6]}")
        print(f"   Size: {row[7]} bytes")
        
        # Simulate what the backend would send to frontend
        message_data = {
            'id': row[0],
            'sender_id': row[1],
            'receiver_id': row[2],
            'message': row[3],
            'message_type': 'file',  # Fixed: Always 'file' for file messages
            'media_type': row[6],    # The actual file type (video, image, etc.)
            'file_type': row[6],     # For frontend compatibility
            'file_name': row[3] or f'{row[6]}_file',  # Original filename
            'file_url': row[5],      # Frontend expects file_url
            'media_url': row[5],     # Backend compatibility
            'file_size': row[7],
            'sent_at': '2025-07-22 18:42:28',
            'is_own': False
        }
        
        print(f"\n✅ Fixed message data structure:")
        print(json.dumps(message_data, indent=2))
        
        # Check if this would be recognized as a video
        is_video = (message_data.get('media_type') == 'video') or \
                  (message_data.get('file_type') and message_data.get('file_type').startswith('video/'))
        
        print(f"\n🎬 Would be recognized as video: {is_video}")
        print(f"   Condition 1 (media_type == 'video'): {message_data.get('media_type') == 'video'}")
        print(f"   Condition 2 (file_type starts with 'video/'): {message_data.get('file_type', '').startswith('video/')}")
        
        # Check if frontend condition would pass
        frontend_condition = (message_data.get('message_type') == 'file' and message_data.get('file_url'))
        print(f"\n📱 Frontend condition would pass: {frontend_condition}")
        print(f"   message_type == 'file': {message_data.get('message_type') == 'file'}")
        print(f"   file_url exists: {bool(message_data.get('file_url'))}")
        
    else:
        print("❌ No video message found with ID 114")
    
    conn.close()

if __name__ == "__main__":
    test_video_upload()
