# Kawaii Chat Application Requirements
# Core web framework
pyngrok
Flask
Werkzeug

# Real-time communication
Flask-SocketIO
python-socketio
python-engineio

# Environment variables
python-dotenv

# Image processing
Pillow

# Cloud storage (Cloudinary)
cloudinary

# Database (if you want to add PostgreSQL support later)
# psycopg2-binary==2.9.7

# Additional useful packages
requests

# Development and testing (optional)
# pytest==7.4.2
# pytest-flask==1.2.0

# Production server (optional)
# gunicorn==21.2.0
# eventlet==0.33.3
