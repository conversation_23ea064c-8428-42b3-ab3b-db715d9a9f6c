// Kawaii Chat - Base JavaScript Functions

class KawaiiEffects {
    constructor() {
        this.init();
    }

    init() {
        this.createFloatingHearts();
        this.addKawaiiAnimations();
        this.initializeTooltips();
    }

    // Create floating hearts background
    createFloatingHearts() {
        const heartsContainer = document.getElementById('floatingHearts');
        if (!heartsContainer) return;

        const hearts = ['💕', '💖', '💗', '💘', '💝', '💞', '🌸', '✨'];
        
        setInterval(() => {
            if (Math.random() < 0.3) { // 30% chance
                this.createFloatingHeart(heartsContainer, hearts);
            }
        }, 2000);
    }

    createFloatingHeart(container, hearts) {
        const heart = document.createElement('div');
        heart.className = 'heart';
        heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
        heart.style.left = Math.random() * 100 + '%';
        heart.style.animationDuration = (Math.random() * 2 + 3) + 's';
        heart.style.fontSize = (Math.random() * 1 + 1) + 'rem';
        
        container.appendChild(heart);
        
        // Remove after animation
        setTimeout(() => {
            if (heart.parentNode) {
                heart.parentNode.removeChild(heart);
            }
        }, 5000);
    }

    // Add kawaii animations to elements
    addKawaiiAnimations() {
        // Animate buttons on hover
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary, .action-btn');
        buttons.forEach(button => {
            button.addEventListener('mouseenter', () => {
                this.createSparkles(button);
            });
        });

        // Animate form inputs
        const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
        inputs.forEach(input => {
            input.addEventListener('focus', () => {
                this.createHeartBurst(input);
            });
        });
    }

    // Create sparkle effect
    createSparkles(element) {
        const sparkles = ['✨', '⭐', '🌟', '💫'];
        const rect = element.getBoundingClientRect();
        
        for (let i = 0; i < 3; i++) {
            const sparkle = document.createElement('div');
            sparkle.className = 'sparkle-effect';
            sparkle.innerHTML = sparkles[Math.floor(Math.random() * sparkles.length)];
            sparkle.style.position = 'fixed';
            sparkle.style.left = (rect.left + Math.random() * rect.width) + 'px';
            sparkle.style.top = (rect.top + Math.random() * rect.height) + 'px';
            sparkle.style.pointerEvents = 'none';
            sparkle.style.zIndex = '1000';
            sparkle.style.animation = 'sparkleFloat 1s ease-out forwards';
            
            document.body.appendChild(sparkle);
            
            setTimeout(() => {
                if (sparkle.parentNode) {
                    sparkle.parentNode.removeChild(sparkle);
                }
            }, 1000);
        }
    }

    // Create heart burst effect
    createHeartBurst(element) {
        const hearts = ['💕', '💖', '💗', '💘', '💝', '💞'];
        const rect = element.getBoundingClientRect();
        
        for (let i = 0; i < 5; i++) {
            const heart = document.createElement('div');
            heart.className = 'heart-burst';
            heart.innerHTML = hearts[Math.floor(Math.random() * hearts.length)];
            heart.style.position = 'fixed';
            heart.style.left = (rect.left + Math.random() * rect.width) + 'px';
            heart.style.top = rect.top + 'px';
            heart.style.pointerEvents = 'none';
            heart.style.zIndex = '1000';
            heart.style.animation = 'heartBurst 1s ease-out forwards';
            
            document.body.appendChild(heart);
            
            setTimeout(() => {
                if (heart.parentNode) {
                    heart.parentNode.removeChild(heart);
                }
            }, 1000);
        }
    }

    // Initialize tooltips
    initializeTooltips() {
        const elementsWithTooltips = document.querySelectorAll('[title]');
        elementsWithTooltips.forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.getAttribute('title'));
            });
            
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    }

    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'kawaii-tooltip';
        tooltip.innerHTML = text;
        tooltip.style.position = 'fixed';
        tooltip.style.background = 'linear-gradient(45deg, #ff6b9d, #c44569)';
        tooltip.style.color = 'white';
        tooltip.style.padding = '0.5rem 1rem';
        tooltip.style.borderRadius = '15px';
        tooltip.style.fontSize = '0.8rem';
        tooltip.style.fontWeight = '600';
        tooltip.style.zIndex = '1001';
        tooltip.style.pointerEvents = 'none';
        tooltip.style.boxShadow = '0 4px 15px rgba(255, 107, 157, 0.3)';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = (rect.left + rect.width / 2 - tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = (rect.top - tooltip.offsetHeight - 10) + 'px';
        
        this.currentTooltip = tooltip;
    }

    hideTooltip() {
        if (this.currentTooltip) {
            this.currentTooltip.remove();
            this.currentTooltip = null;
        }
    }
}

// Utility Functions
function showToast(message, type = 'success') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = `<i class="fas fa-heart"></i> ${message}`;
    
    document.body.appendChild(toast);
    
    setTimeout(() => toast.classList.add('show'), 100);
    
    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

function createConfetti() {
    const confetti = ['🎉', '🎊', '✨', '🌟', '💖', '🌸', '🦄', '🌈'];
    
    for (let i = 0; i < 20; i++) {
        const piece = document.createElement('div');
        piece.innerHTML = confetti[Math.floor(Math.random() * confetti.length)];
        piece.style.position = 'fixed';
        piece.style.left = Math.random() * 100 + 'vw';
        piece.style.top = '-10px';
        piece.style.fontSize = (Math.random() * 1.5 + 1) + 'rem';
        piece.style.pointerEvents = 'none';
        piece.style.zIndex = '1000';
        piece.style.animation = `confettiFall ${Math.random() * 2 + 2}s ease-out forwards`;
        
        document.body.appendChild(piece);
        
        setTimeout(() => {
            if (piece.parentNode) {
                piece.parentNode.removeChild(piece);
            }
        }, 4000);
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes sparkleFloat {
        0% {
            transform: translateY(0) scale(0);
            opacity: 1;
        }
        100% {
            transform: translateY(-30px) scale(1);
            opacity: 0;
        }
    }
    
    @keyframes confettiFall {
        0% {
            transform: translateY(-10px) rotate(0deg);
            opacity: 1;
        }
        100% {
            transform: translateY(100vh) rotate(360deg);
            opacity: 0;
        }
    }
    
    .kawaii-tooltip {
        animation: tooltipAppear 0.2s ease-out;
    }
    
    @keyframes tooltipAppear {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);

// Initialize kawaii effects when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new KawaiiEffects();
    
    // Add some console kawaii messages
    console.log('%c🌸 Kawaii Chat Loaded! 🌸', 'color: #ff6b9d; font-size: 20px; font-weight: bold;');
    console.log('%c✨ Made with love and lots of pink! ✨', 'color: #c44569; font-size: 14px;');
});

// Export for use in other files
window.KawaiiEffects = KawaiiEffects;
window.showToast = showToast;
window.createConfetti = createConfetti;
