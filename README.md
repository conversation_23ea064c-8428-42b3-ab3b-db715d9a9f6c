# 🌸 Kawaii Chat

A modern, real-time chat application built with Flask and Socket.IO, featuring a cute kawaii-themed interface with comprehensive messaging capabilities.

## ✨ Features

### 💬 Real-time Messaging
- **Instant messaging** with Socket.IO for real-time communication
- **Message delivery status** with read receipts
- **Reply to messages** with threaded conversations
- **Message reactions** with emoji support
- **Online/offline status** indicators with last seen timestamps

### 📱 Media Support
- **File sharing** (images, videos, audio, documents)
- **Image thumbnails** with automatic generation
- **Profile pictures** with Cloudinary integration
- **File size limits** (50MB for messages, 10MB for profile pictures)

### 📞 Voice & Video Calls
- **Audio calls** with WebRTC integration
- **Video calls** with camera support
- **Call history** tracking
- **Call status** management (initiated, ringing, accepted, rejected, ended)

### 👥 Contact Management
- **Unique contact codes** for easy friend discovery
- **Custom contact names** for personalization
- **Contact list** with avatar colors and profile pictures

### 🎨 User Interface
- **Kawaii-themed design** with cute aesthetics
- **Responsive layout** for mobile and desktop
- **Facebook Messenger-style** interface
- **Virtual keyboard support** for mobile devices
- **Dark/light theme** compatibility

### 🔐 Security & Authentication
- **User registration** with password hashing
- **Session management** with Flask sessions
- **Secure file uploads** with validation
- **CSRF protection** and input sanitization

## 🛠️ Technology Stack

### Backend
- **Flask** - Web framework
- **Flask-SocketIO** - Real-time communication
- **SQLite/PostgreSQL** - Database (dual support)
- **Werkzeug** - Security utilities
- **Cloudinary** - Media storage and processing

### Frontend
- **HTML5/CSS3** - Modern web standards
- **JavaScript (ES6+)** - Interactive functionality
- **Socket.IO Client** - Real-time communication
- **WebRTC** - Voice and video calling
- **Font Awesome** - Icons

### Infrastructure
- **ngrok** - Public tunnel for development
- **python-dotenv** - Environment configuration
- **Pillow** - Image processing

## 📋 Prerequisites

- Python 3.8 or higher
- pip (Python package installer)
- A Cloudinary account (for media storage)
- ngrok account (for public access)

## 🚀 Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd project
   ```

2. **Create a virtual environment**
   ```bash
   python -m venv hr
   # On Windows:
   hr\Scripts\activate
   # On macOS/Linux:
   source hr/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   SECRET_KEY=your-secret-key-here
   CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
   CLOUDINARY_API_KEY=your-cloudinary-api-key
   CLOUDINARY_API_SECRET=your-cloudinary-api-secret
   DATABASE_URL=sqlite:///kawaii_chat.db  # or PostgreSQL URL
   ```

5. **Initialize the database**
   ```bash
   python app.py
   ```
   The database will be automatically created on first run.

## 🎮 Usage

### Starting the Application

1. **Activate virtual environment**
   ```bash
   # Windows:
   hr\Scripts\activate
   # macOS/Linux:
   source hr/bin/activate
   ```

2. **Run the application**
   ```bash
   python app.py
   ```

3. **Access the application**
   - Local: `http://localhost:5000`
   - Public (via ngrok): Check console output for the public URL

### User Registration

1. Navigate to the signup page
2. Choose a unique username
3. Set a secure password (minimum 6 characters)
4. You'll receive a unique 8-character contact code

### Adding Contacts

1. Get a friend's contact code
2. Click "Add Contact" in the navigation
3. Enter their contact code and optional display name
4. Start chatting immediately!

### Messaging Features

- **Text messages**: Type and send instantly
- **File sharing**: Click the attachment icon to share media
- **Voice messages**: Record and send audio clips
- **Reactions**: Long-press messages to add emoji reactions
- **Replies**: Click reply to respond to specific messages

### Voice & Video Calls

1. Open a chat with a contact
2. Click the phone icon for audio calls
3. Click the video icon for video calls
4. Accept/reject incoming calls with the provided controls

## 📁 Project Structure

```
project/
├── app.py                 # Main application file
├── requirements.txt       # Python dependencies
├── README.md             # This file
├── .env                  # Environment variables (create this)
├── kawaii_chat.db        # SQLite database (auto-generated)
├── templates/            # HTML templates
│   ├── base.html         # Base template
│   ├── chat.html         # Main chat interface
│   ├── login.html        # Login page
│   ├── signup.html       # Registration page
│   └── profile.html      # User profile page
├── static/               # Static assets
│   ├── css/
│   │   └── style.css     # Main stylesheet
│   └── js/
│       ├── chat.js       # Chat functionality
│       ├── kawaii.js     # UI interactions
│       ├── profile.js    # Profile management
│       └── webrtc.js     # Voice/video calling
└── hr/                   # Virtual environment
```

## 🔧 Configuration

### Database Options

**SQLite (Default)**
- Automatically created as `kawaii_chat.db`
- Perfect for development and small deployments

**PostgreSQL (Production)**
- Set `DATABASE_URL` to your PostgreSQL connection string
- Format: `postgresql://user:password@host:port/database`

### Cloudinary Setup

1. Create a free account at [Cloudinary](https://cloudinary.com/)
2. Get your cloud name, API key, and API secret
3. Add them to your `.env` file
4. Used for profile pictures and media file storage

### ngrok Configuration

1. Sign up at [ngrok](https://ngrok.com/)
2. Get your auth token
3. Update `NGROK_AUTH_TOKEN` in `app.py` (line 53)
4. Enables public access to your local development server

## 🚀 Deployment

### Local Development
- Run with `python app.py`
- Access via `localhost:5000` or ngrok URL

### Production Deployment
- Use a production WSGI server like Gunicorn
- Set up PostgreSQL database
- Configure environment variables
- Use a reverse proxy (nginx) for static files

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🐛 Known Issues

- Check `FACEBOOK_MESSENGER_LAYOUT_IMPLEMENTED.md` for UI improvements
- Check `VIRTUAL_KEYBOARD_FIX_IMPLEMENTED.md` for mobile fixes
- WebRTC calls may require HTTPS in production

## 📞 Support

For support, please open an issue in the repository or contact the development team.

---

Made with 💖 and lots of kawaii energy! 🌸✨
